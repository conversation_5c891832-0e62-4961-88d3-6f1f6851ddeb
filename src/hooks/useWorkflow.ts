import { useState, useCallback } from 'react'
import { useNodesState, useEdgesState, addEdge, Connection, Edge } from '@xyflow/react'
import { WorkflowNode, WorkflowEdge, Workflow } from '../types/workflow'
import { createNode, validateWorkflow } from '../utils/nodeUtils'
import { v4 as uuidv4 } from 'uuid'

export const useWorkflow = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState<WorkflowNode>([])
  const [edges, setEdges, onEdgesChange] = useEdgesState<WorkflowEdge>([])
  const [workflow, setWorkflow] = useState<Workflow | null>(null)

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge: WorkflowEdge = {
        ...params,
        id: uuidv4(),
        type: 'smoothstep',
        animated: true,
      }
      setEdges((eds) => addEdge(newEdge, eds))
    },
    [setEdges]
  )

  const addNode = useCallback((type: WorkflowNode['type'], position: { x: number; y: number }) => {
    const newNode = createNode(type, position)
    setNodes((nds) => [...nds, newNode])
  }, [setNodes])

  const updateNodeData = useCallback((nodeId: string, data: Partial<WorkflowNode['data']>) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId ? { ...node, data: { ...node.data, ...data } } : node
      )
    )
  }, [setNodes])

  const deleteNode = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId))
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId))
  }, [setNodes, setEdges])

  const clearWorkflow = useCallback(() => {
    setNodes([])
    setEdges([])
    setWorkflow(null)
  }, [setNodes, setEdges])

  const saveWorkflow = useCallback((name: string, description?: string) => {
    const newWorkflow: Workflow = {
      id: uuidv4(),
      name,
      description,
      nodes,
      edges,
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    setWorkflow(newWorkflow)
    
    // Save to localStorage for persistence
    const savedWorkflows = JSON.parse(localStorage.getItem('workflows') || '[]')
    savedWorkflows.push(newWorkflow)
    localStorage.setItem('workflows', JSON.stringify(savedWorkflows))
    
    return newWorkflow
  }, [nodes, edges])

  const loadWorkflow = useCallback((workflowData: Workflow) => {
    setNodes(workflowData.nodes)
    setEdges(workflowData.edges)
    setWorkflow(workflowData)
  }, [setNodes, setEdges])

  const validateCurrentWorkflow = useCallback(() => {
    return validateWorkflow(nodes)
  }, [nodes])

  return {
    nodes,
    edges,
    workflow,
    onNodesChange,
    onEdgesChange,
    onConnect,
    addNode,
    updateNodeData,
    deleteNode,
    clearWorkflow,
    saveWorkflow,
    loadWorkflow,
    validateCurrentWorkflow,
  }
}
