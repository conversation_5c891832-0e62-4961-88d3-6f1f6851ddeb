import { Node, Edge } from '@xyflow/react'

export type NodeType = 'start' | 'process' | 'decision' | 'end'

export interface WorkflowNodeData {
  label: string
  description?: string
  config?: Record<string, any>
}

export interface WorkflowNode extends Node {
  type: NodeType
  data: WorkflowNodeData
}

export interface WorkflowEdge extends Edge {
  label?: string
}

export interface Workflow {
  id: string
  name: string
  description?: string
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  createdAt: Date
  updatedAt: Date
}

export interface NodeTemplate {
  type: NodeType
  label: string
  description: string
  icon: string
  defaultData: WorkflowNodeData
}
