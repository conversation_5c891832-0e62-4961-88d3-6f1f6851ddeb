import React from 'react'
import { NodeType } from '../types/workflow'
import { getNodeIcon } from '../utils/nodeUtils'

interface SidebarProps {
  onAddNode: (type: NodeType) => void
  onClearWorkflow: () => void
  onSaveWorkflow: () => void
  validationErrors: string[]
}

const nodeTypes: Array<{ type: NodeType; label: string; description: string }> = [
  { type: 'start', label: 'Start', description: 'Beginning of workflow' },
  { type: 'process', label: 'Process', description: 'Action or task' },
  { type: 'decision', label: 'Decision', description: 'Conditional branch' },
  { type: 'end', label: 'End', description: 'End of workflow' },
]

const Sidebar: React.FC<SidebarProps> = ({ 
  onAddNode, 
  onClearWorkflow, 
  onSaveWorkflow, 
  validationErrors 
}) => {
  const handleDragStart = (event: React.DragEvent, nodeType: NodeType) => {
    event.dataTransfer.setData('application/reactflow', nodeType)
    event.dataTransfer.effectAllowed = 'move'
  }

  return (
    <div className="w-64 bg-gray-100 border-r border-gray-300 p-4 flex flex-col h-full">
      <h2 className="text-xl font-bold mb-4 text-gray-800">Workflow Builder</h2>
      
      {/* Node Types */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700">Node Types</h3>
        <div className="space-y-2">
          {nodeTypes.map((nodeType) => (
            <div
              key={nodeType.type}
              className="p-3 bg-white rounded-lg shadow-sm border border-gray-200 cursor-move hover:shadow-md transition-shadow"
              draggable
              onDragStart={(e) => handleDragStart(e, nodeType.type)}
              onClick={() => onAddNode(nodeType.type)}
            >
              <div className="flex items-center mb-1">
                <span className="mr-2">{getNodeIcon(nodeType.type)}</span>
                <span className="font-medium text-gray-800">{nodeType.label}</span>
              </div>
              <p className="text-sm text-gray-600">{nodeType.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3 text-gray-700">Actions</h3>
        <div className="space-y-2">
          <button
            onClick={onSaveWorkflow}
            className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Save Workflow
          </button>
          <button
            onClick={onClearWorkflow}
            className="w-full px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            Clear All
          </button>
        </div>
      </div>

      {/* Validation */}
      {validationErrors.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3 text-red-700">Validation Errors</h3>
          <div className="space-y-1">
            {validationErrors.map((error, index) => (
              <div key={index} className="text-sm text-red-600 bg-red-50 p-2 rounded">
                {error}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="mt-auto">
        <h3 className="text-sm font-semibold mb-2 text-gray-700">Instructions</h3>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• Drag nodes from sidebar to canvas</li>
          <li>• Click nodes to add them at center</li>
          <li>• Double-click nodes to edit</li>
          <li>• Connect nodes by dragging handles</li>
          <li>• Delete nodes with Delete key</li>
        </ul>
      </div>
    </div>
  )
}

export default Sidebar
