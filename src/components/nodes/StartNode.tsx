import React from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'
import { WorkflowNodeData } from '../../types/workflow'

const StartNode: React.FC<NodeProps<WorkflowNodeData>> = ({ data, selected }) => {
  return (
    <div className={`px-4 py-2 shadow-md rounded-md bg-green-500 text-white border-2 ${
      selected ? 'border-green-700' : 'border-green-600'
    }`}>
      <div className="flex items-center">
        <div className="mr-2">▶️</div>
        <div>
          <div className="text-lg font-bold">{data.label}</div>
          {data.description && (
            <div className="text-sm opacity-80">{data.description}</div>
          )}
        </div>
      </div>
      
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-green-700"
      />
    </div>
  )
}

export default StartNode
