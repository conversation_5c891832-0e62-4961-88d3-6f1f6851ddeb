import React, { useState } from 'react'
import { Handle, Position, NodeProps } from '@xyflow/react'
import { WorkflowNodeData } from '../../types/workflow'

const DecisionNode: React.FC<NodeProps<WorkflowNodeData>> = ({ data, selected }) => {
  const [isEditing, setIsEditing] = useState(false)
  const [label, setLabel] = useState(data.label)
  const [description, setDescription] = useState(data.description || '')

  const handleDoubleClick = () => {
    setIsEditing(true)
  }

  const handleSave = () => {
    setIsEditing(false)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave()
    }
    if (e.key === 'Escape') {
      setIsEditing(false)
      setLabel(data.label)
      setDescription(data.description || '')
    }
  }

  return (
    <div className={`px-4 py-2 shadow-md rounded-md bg-yellow-500 text-black border-2 ${
      selected ? 'border-yellow-700' : 'border-yellow-600'
    }`} onDoubleClick={handleDoubleClick}>
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-yellow-700"
      />
      
      <div className="flex items-center">
        <div className="mr-2">❓</div>
        <div className="min-w-0 flex-1">
          {isEditing ? (
            <div className="space-y-1">
              <input
                type="text"
                value={label}
                onChange={(e) => setLabel(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={handleSave}
                className="text-lg font-bold bg-transparent border-b border-black outline-none w-full"
                autoFocus
              />
              <input
                type="text"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={handleSave}
                className="text-sm bg-transparent border-b border-black outline-none w-full opacity-80"
                placeholder="Condition..."
              />
            </div>
          ) : (
            <div>
              <div className="text-lg font-bold">{data.label}</div>
              {data.description && (
                <div className="text-sm opacity-80">{data.description}</div>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* Yes/True output */}
      <Handle
        type="source"
        position={Position.Right}
        id="yes"
        className="w-3 h-3 bg-green-600"
        style={{ top: '30%' }}
      />
      
      {/* No/False output */}
      <Handle
        type="source"
        position={Position.Right}
        id="no"
        className="w-3 h-3 bg-red-600"
        style={{ top: '70%' }}
      />
    </div>
  )
}

export default DecisionNode
