import React from 'react'
import { NodeProps } from '@xyflow/react'
import { WorkflowNodeData } from '../../types/workflow'
import BaseNode from './BaseNode'
import { Position } from '@xyflow/react'
import { BaseNodeConfig } from './BaseNode'

// Ví dụ cấu hình cho một node tùy chỉnh với hình thôi
const customNodeConfig: BaseNodeConfig = {
  backgroundColor: 'bg-purple-500',
  borderColor: 'border-purple-600',
  selectedBorderColor: 'border-purple-700',
  textColor: 'text-white',
  icon: '🔮',
  shape: 'diamond', // Sử dụng hình thôi
  handles: {
    target: [
      {
        position: Position.Left,
        className: 'w-3 h-3 bg-purple-700',
        style: {
          left: '0px',
          top: '50%',
          transform: 'translate(-50%, -50%)'
        }
      }
    ],
    source: [
      {
        position: Position.Top,
        className: 'w-3 h-3 bg-purple-700',
        style: {
          left: '50%',
          top: '0px',
          transform: 'translate(-50%, -50%)'
        }
      },
      {
        position: Position.Right,
        className: 'w-3 h-3 bg-purple-700',
        style: {
          left: '100%',
          top: '50%',
          transform: 'translate(-50%, -50%)'
        }
      },
      {
        position: Position.Bottom,
        className: 'w-3 h-3 bg-purple-700',
        style: {
          left: '50%',
          top: '100%',
          transform: 'translate(-50%, -50%)'
        }
      }
    ]
  },
  editable: true
}

const ExampleCustomNode: React.FC<NodeProps<WorkflowNodeData>> = (props) => {
  return (
    <BaseNode
      {...props}
      config={customNodeConfig}
    >
      {/* Có thể thêm custom content ở đây */}
      <div className="mt-2 text-xs opacity-75">
        Custom Node Example
      </div>
    </BaseNode>
  )
}

export default ExampleCustomNode
