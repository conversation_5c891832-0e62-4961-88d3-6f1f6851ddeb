import React from 'react'
import { <PERSON>le, Position, NodeProps } from '@xyflow/react'
import { WorkflowNodeData } from '../../types/workflow'

const EndNode: React.FC<NodeProps<WorkflowNodeData>> = ({ data, selected }) => {
  return (
    <div className={`px-4 py-2 shadow-md rounded-md bg-red-500 text-white border-2 ${
      selected ? 'border-red-700' : 'border-red-600'
    }`}>
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-red-700"
      />
      
      <div className="flex items-center">
        <div className="mr-2">🏁</div>
        <div>
          <div className="text-lg font-bold">{data.label}</div>
          {data.description && (
            <div className="text-sm opacity-80">{data.description}</div>
          )}
        </div>
      </div>
    </div>
  )
}

export default EndNode
