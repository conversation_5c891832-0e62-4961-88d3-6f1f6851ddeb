# Diamond Node Handles - Cải tiến vị trí handles

## Vấn đề trước đây
- <PERSON><PERSON> được đặt ở giữa các cạnh của hình thôi
- Trông không tự nhiên khi kết nối edges
- Không tuân theo chuẩn flowchart truyền thống

## Giải pháp mới
- <PERSON><PERSON> được đặt tại **4 đỉnh** của hình thôi
- Sử dụng CSS positioning để đặt chính xác

## Cách thực hiện

### CSS Positioning
```tsx
// Đỉnh trái (Target)
{
  position: Position.Left,
  style: {
    left: '0px',           // Sát mép trái
    top: '50%',            // Giữa chiều cao
    transform: 'translate(-50%, -50%)'  // Căn giữa handle
  }
}

// Đỉnh trên (Source - Yes)
{
  position: Position.Top,
  style: {
    left: '50%',           // Giữa chiều rộng
    top: '0px',            // <PERSON><PERSON><PERSON> mép trên
    transform: 'translate(-50%, -50%)'
  }
}

// Đỉnh phải (Source - No)
{
  position: Position.Right,
  style: {
    left: '100%',          // Sát mép phải
    top: '50%',            // Giữa chiều cao
    transform: 'translate(-50%, -50%)'
  }
}

// Đỉnh dưới (Source - Maybe)
{
  position: Position.Bottom,
  style: {
    left: '50%',           // Giữa chiều rộng
    top: '100%',           // Sát mép dưới
    transform: 'translate(-50%, -50%)'
  }
}
```

## Kết quả

### DecisionNode hiện tại:
- **1 Target handle**: Đỉnh trái (input)
- **3 Source handles**: 
  - Đỉnh trên (Yes - xanh lá)
  - Đỉnh phải (No - đỏ)  
  - Đỉnh dưới (Maybe - xanh dương)

### Ưu điểm:
1. **Tự nhiên**: Edges kết nối tại các đỉnh như flowchart thật
2. **Rõ ràng**: Dễ phân biệt hướng đi của workflow
3. **Chuẩn**: Tuân theo convention của flowchart
4. **Thẩm mỹ**: Trông đẹp và chuyên nghiệp hơn

### So sánh:

#### Trước (handles ở cạnh):
```
    ○ (top)
   ╱ ╲
○ ╱   ╲ ○ (right)
  ╲   ╱
   ╲ ╱
    ○ (bottom)
```

#### Sau (handles ở đỉnh):
```
    ○
   ╱ ╲
  ╱   ╲
○╱     ╲○
 ╲     ╱
  ╲   ╱
   ╲ ╱
    ○
```

## Áp dụng cho node khác

Bất kỳ node nào sử dụng `shape: 'diamond'` đều có thể áp dụng pattern này:

```tsx
const myDiamondNode: BaseNodeConfig = {
  // ... other config
  shape: 'diamond',
  handles: {
    target: [
      {
        position: Position.Left,
        style: {
          left: '0px',
          top: '50%',
          transform: 'translate(-50%, -50%)'
        }
      }
    ],
    source: [
      // Các đỉnh còn lại tùy theo nhu cầu
    ]
  }
}
```

## Lưu ý kỹ thuật

1. **Transform**: `translate(-50%, -50%)` để căn giữa handle
2. **Positioning**: Sử dụng `0px`, `50%`, `100%` cho chính xác
3. **Z-index**: Handles tự động có z-index cao hơn node
4. **Responsive**: Hoạt động tốt ở mọi kích thước màn hình
