# Diamond Node Handles - Cải tiến vị trí handles

## Vấn đề trước đây
- <PERSON><PERSON> được đặt ở giữa các cạnh của hình thôi
- Trông không tự nhiên khi kết nối edges
- <PERSON>h<PERSON>ng tuân theo chuẩn flowchart truyền thống

## Giải pháp mới
- <PERSON><PERSON> được đặt tại **4 đỉnh** của hình thôi
- Sử dụng CSS positioning để đặt chính xác

## Cách thực hiện

### CSS Positioning - Đỉnh thực sự của hình thôi

Vì hình thôi được tạo bằng cách xoay hình vuông 45°, các đỉnh thực sự cần được tính toán lại:

```tsx
// Đỉnh trái (Target) - Đỉnh thực sự của hình thôi
{
  position: Position.Left,
  style: {
    left: '50%',
    top: '50%',
    // B<PERSON><PERSON> đầu từ tâm -> xoay ngược -> di chuyển đến đỉnh -> xoay lại
    transform: 'translate(-50%, -50%) rotate(-45deg) translateX(-48px) rotate(45deg)'
  }
}

// Đỉnh trên (Source - Yes)
{
  position: Position.Top,
  style: {
    left: '50%',
    top: '50%',
    transform: 'translate(-50%, -50%) rotate(-45deg) translateY(-48px) rotate(45deg)'
  }
}

// Đỉnh phải (Source - No)
{
  position: Position.Right,
  style: {
    left: '50%',
    top: '50%',
    transform: 'translate(-50%, -50%) rotate(-45deg) translateX(48px) rotate(45deg)'
  }
}

// Đỉnh dưới (Source - Maybe)
{
  position: Position.Bottom,
  style: {
    left: '50%',
    top: '50%',
    transform: 'translate(-50%, -50%) rotate(-45deg) translateY(48px) rotate(45deg)'
  }
}
```

### Giải thích transform:
1. `translate(-50%, -50%)`: Đưa handle về tâm node
2. `rotate(-45deg)`: Xoay ngược lại để bù trừ rotation của diamond
3. `translateX/Y(48px)`: Di chuyển đến đỉnh (48px = nửa đường chéo của hình vuông 96x96)
4. `rotate(45deg)`: Xoay lại để handle thẳng

## Kết quả

### DecisionNode hiện tại:
- **1 Target handle**: Đỉnh trái (input)
- **3 Source handles**:
  - Đỉnh trên (Yes - xanh lá)
  - Đỉnh phải (No - đỏ)
  - Đỉnh dưới (Maybe - xanh dương)

### Ưu điểm:
1. **Tự nhiên**: Edges kết nối tại các đỉnh như flowchart thật
2. **Rõ ràng**: Dễ phân biệt hướng đi của workflow
3. **Chuẩn**: Tuân theo convention của flowchart
4. **Thẩm mỹ**: Trông đẹp và chuyên nghiệp hơn

### So sánh:

#### Trước (handles ở giữa cạnh - SAI):
```
   ○
  ╱ ╲
 ○   ○  <- Handles ở giữa cạnh
  ╲ ╱
   ○
```

#### Sau (handles ở đỉnh thực sự - ĐÚNGG):
```
    ○  <- Đỉnh trên
   ╱ ╲
  ╱   ╲
○     ○ <- Đỉnh trái và phải
  ╲   ╱
   ╲ ╱
    ○  <- Đỉnh dưới
```

### Tại sao cần transform phức tạp?

Vì hình thôi được tạo bằng:
```css
transform: rotate(45deg)  /* Xoay hình vuông thành hình thôi */
```

Nên để đặt handles ở đỉnh thực sự, ta cần:
1. Bắt đầu từ tâm hình thôi
2. "Hủy" rotation để về hệ toạ độ gốc
3. Di chuyển đến đỉnh của hình vuông gốc
4. Xoay lại để handle thẳng

## Áp dụng cho node khác

Bất kỳ node nào sử dụng `shape: 'diamond'` đều có thể áp dụng pattern này:

```tsx
const myDiamondNode: BaseNodeConfig = {
  // ... other config
  shape: 'diamond',
  handles: {
    target: [
      {
        position: Position.Left,
        style: {
          left: '0px',
          top: '50%',
          transform: 'translate(-50%, -50%)'
        }
      }
    ],
    source: [
      // Các đỉnh còn lại tùy theo nhu cầu
    ]
  }
}
```

## Lưu ý kỹ thuật

1. **Transform**: `translate(-50%, -50%)` để căn giữa handle
2. **Positioning**: Sử dụng `0px`, `50%`, `100%` cho chính xác
3. **Z-index**: Handles tự động có z-index cao hơn node
4. **Responsive**: Hoạt động tốt ở mọi kích thước màn hình
