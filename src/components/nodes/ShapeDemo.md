# Demo Shape cho BaseNode

## Các hình dạng được hỗ trợ

### 1. Rectangle (<PERSON><PERSON><PERSON> chữ nhật)
- **Sử dụng**: Cho các node thông thường như Start, Process, End
- **Đặc điểm**: 
  - Layout ngang với icon và text
  - Padding `px-4 py-2`
  - Border radius `rounded-md`
  - Hỗ trợ inline editing đầy đủ

### 2. Diamond (<PERSON><PERSON><PERSON> thôi)
- **Sử dụng**: Cho các node quyết định/điều kiện như Decision
- **Đặc điểm**:
  - Xoay 45 độ để tạo hình thôi
  - Kích thước cố định `w-24 h-24`
  - Content được xoay ngược lại -45 độ
  - Text nhỏ hơn để vừa trong hình thôi
  - Handles được định vị đặc biệt

## C<PERSON><PERSON> handles được định vị

### Rectangle Nodes
```tsx
// Target handle - bên trái
{
  position: Position.Left,
  className: 'w-3 h-3 bg-blue-700'
}

// Source handle - bên phải  
{
  position: Position.Right,
  className: 'w-3 h-3 bg-blue-700'
}
```

### Diamond Nodes
```tsx
// Target handle - bên trái với offset
{
  position: Position.Left,
  className: 'w-3 h-3 bg-yellow-700',
  style: { left: '-6px', top: '50%', transform: 'translateY(-50%)' }
}

// Source handles - trên và dưới
{
  position: Position.Top,
  id: 'yes',
  className: 'w-3 h-3 bg-green-600',
  style: { top: '-6px', left: '50%', transform: 'translateX(-50%)' }
},
{
  position: Position.Bottom,
  id: 'no', 
  className: 'w-3 h-3 bg-red-600',
  style: { bottom: '-6px', left: '50%', transform: 'translateX(-50%)' }
}
```

## Ví dụ sử dụng

### Node hình chữ nhật
```tsx
const rectangleConfig: BaseNodeConfig = {
  backgroundColor: 'bg-blue-500',
  borderColor: 'border-blue-600',
  selectedBorderColor: 'border-blue-700',
  textColor: 'text-white',
  icon: '⚙️',
  shape: 'rectangle', // hoặc không cần khai báo (default)
  // ... handles config
}
```

### Node hình thôi
```tsx
const diamondConfig: BaseNodeConfig = {
  backgroundColor: 'bg-yellow-500',
  borderColor: 'border-yellow-600', 
  selectedBorderColor: 'border-yellow-700',
  textColor: 'text-black',
  icon: '🔀',
  shape: 'diamond',
  // ... handles config với style đặc biệt
}
```

## Lưu ý khi thiết kế

1. **Diamond nodes**: Text nên ngắn gọn vì không gian hạn chế
2. **Handle positioning**: Diamond cần style đặc biệt để handles nằm đúng vị trí
3. **Icon**: Chọn icon phù hợp với hình dạng và ý nghĩa của node
4. **Colors**: Màu sắc nên phản ánh chức năng (vàng cho decision, xanh cho process, v.v.)

## Kết quả

- **StartNode**: Rectangle, xanh lá, icon ▶️
- **ProcessNode**: Rectangle, xanh dương, icon ⚙️  
- **DecisionNode**: Diamond, vàng, icon 🔀
- **EndNode**: Rectangle, đỏ, icon 🏁
