import { Position } from '@xyflow/react'
import { BaseNodeConfig } from './BaseNode'
import { NodeType } from '../../types/workflow'

export const nodeConfigs: Record<NodeType, BaseNodeConfig> = {
  start: {
    backgroundColor: 'bg-green-500',
    borderColor: 'border-green-600',
    selectedBorderColor: 'border-green-700',
    textColor: 'text-white',
    icon: '▶️',
    shape: 'rectangle',
    handles: {
      source: [
        {
          position: Position.Right,
          className: 'w-3 h-3 bg-green-700'
        }
      ]
    },
    editable: false
  },

  process: {
    backgroundColor: 'bg-blue-500',
    borderColor: 'border-blue-600',
    selectedBorderColor: 'border-blue-700',
    textColor: 'text-white',
    icon: '⚙️',
    shape: 'rectangle',
    handles: {
      target: [
        {
          position: Position.Left,
          className: 'w-3 h-3 bg-blue-700'
        }
      ],
      source: [
        {
          position: Position.Right,
          className: 'w-3 h-3 bg-blue-700'
        }
      ]
    },
    editable: true
  },

  decision: {
    backgroundColor: 'bg-yellow-500',
    borderColor: 'border-yellow-600',
    selectedBorderColor: 'border-yellow-700',
    textColor: 'text-black',
    icon: '🔀', // Icon condition/decision
    shape: 'diamond',
    handles: {
      target: [
        {
          position: Position.Left,
          className: 'w-3 h-3 bg-yellow-700',
          style: {
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%) rotate(-45deg) translateX(-48px) rotate(45deg)'
          }
        }
      ],
      source: [
        {
          position: Position.Top,
          id: 'yes',
          className: 'w-3 h-3 bg-green-600',
          style: {
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%) rotate(-45deg) translateY(-48px) rotate(45deg)'
          }
        },
        {
          position: Position.Right,
          id: 'no',
          className: 'w-3 h-3 bg-red-600',
          style: {
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%) rotate(-45deg) translateX(48px) rotate(45deg)'
          }
        },
        {
          position: Position.Bottom,
          id: 'maybe',
          className: 'w-3 h-3 bg-blue-600',
          style: {
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%) rotate(-45deg) translateY(48px) rotate(45deg)'
          }
        }
      ]
    },
    editable: true
  },

  end: {
    backgroundColor: 'bg-red-500',
    borderColor: 'border-red-600',
    selectedBorderColor: 'border-red-700',
    textColor: 'text-white',
    icon: '🏁',
    shape: 'rectangle',
    handles: {
      target: [
        {
          position: Position.Left,
          className: 'w-3 h-3 bg-red-700'
        }
      ]
    },
    editable: false
  }
}
