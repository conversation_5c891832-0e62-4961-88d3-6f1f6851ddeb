{"name": "react-workflow-builder-ts", "version": "0.0.1", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@xyflow/react": "^12.6.4", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^4.0.0"}}